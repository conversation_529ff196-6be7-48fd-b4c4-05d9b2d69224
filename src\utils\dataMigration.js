// Data Migration Utility for Firebase
import { FirestoreService, COLLECTIONS } from '../services/firebase';

// Import existing dummy data
import { galleryImages } from '../data/galleryImages';
import { blogPosts } from '../data/blogPosts';
import { caseStudies } from '../data/caseStudies';
import { testimonials } from '../data/testimonials';
import { resources } from '../data/resources';
import { contactMessages } from '../data/contactMessages';

/**
 * Data migration utility to move dummy data to Firebase Firestore
 */
export class DataMigration {
  /**
   * Migrate all dummy data to Firestore
   * @param {string} createdBy - User ID who is performing the migration
   * @returns {Promise<{success: boolean, results?: object, error?: string}>}
   */
  static async migrateAllData(createdBy = 'system') {
    console.log('Starting data migration to Firebase...');
    
    const results = {
      galleryImages: { success: false, count: 0 },
      blogPosts: { success: false, count: 0 },
      caseStudies: { success: false, count: 0 },
      testimonials: { success: false, count: 0 },
      resources: { success: false, count: 0 },
      contactMessages: { success: false, count: 0 }
    };

    try {
      // Migrate gallery images
      console.log('Migrating gallery images...');
      const galleryResult = await this.migrateGalleryImages(createdBy);
      results.galleryImages = galleryResult;

      // Migrate blog posts
      console.log('Migrating blog posts...');
      const blogResult = await this.migrateBlogPosts(createdBy);
      results.blogPosts = blogResult;

      // Migrate case studies
      console.log('Migrating case studies...');
      const caseStudiesResult = await this.migrateCaseStudies(createdBy);
      results.caseStudies = caseStudiesResult;

      // Migrate testimonials
      console.log('Migrating testimonials...');
      const testimonialsResult = await this.migrateTestimonials(createdBy);
      results.testimonials = testimonialsResult;

      // Migrate resources
      console.log('Migrating resources...');
      const resourcesResult = await this.migrateResources(createdBy);
      results.resources = resourcesResult;

      // Migrate contact messages
      console.log('Migrating contact messages...');
      const contactResult = await this.migrateContactMessages();
      results.contactMessages = contactResult;

      console.log('Data migration completed!', results);
      
      return {
        success: true,
        results
      };
    } catch (error) {
      console.error('Data migration failed:', error);
      return {
        success: false,
        error: error.message,
        results
      };
    }
  }

  /**
   * Migrate gallery images to Firestore
   */
  static async migrateGalleryImages(createdBy) {
    try {
      let successCount = 0;
      const errors = [];

      for (const image of galleryImages) {
        const imageData = {
          title: image.title,
          alt: image.alt,
          category: image.category,
          size: image.size,
          description: image.description,
          imageUrl: image.src,
          storageRef: null // Will be updated when images are uploaded to Firebase Storage
        };

        const result = await FirestoreService.addDocument(
          COLLECTIONS.GALLERY_IMAGES,
          imageData,
          createdBy
        );

        if (result.success) {
          successCount++;
        } else {
          errors.push(`Failed to migrate image "${image.title}": ${result.error}`);
        }
      }

      return {
        success: errors.length === 0,
        count: successCount,
        total: galleryImages.length,
        errors
      };
    } catch (error) {
      return {
        success: false,
        count: 0,
        total: galleryImages.length,
        error: error.message
      };
    }
  }

  /**
   * Migrate blog posts to Firestore
   */
  static async migrateBlogPosts(createdBy) {
    try {
      let successCount = 0;
      const errors = [];

      for (const post of blogPosts) {
        const postData = {
          title: post.title,
          slug: post.slug,
          excerpt: post.excerpt,
          content: post.content,
          featuredImageUrl: post.featured_image_url,
          category: post.category,
          tags: post.tags || [],
          status: post.status || 'published',
          publishedAt: post.published_date ? new Date(post.published_date) : new Date(),
          readTime: post.read_time || 5
        };

        const result = await FirestoreService.addDocument(
          COLLECTIONS.BLOG_POSTS,
          postData,
          createdBy
        );

        if (result.success) {
          successCount++;
        } else {
          errors.push(`Failed to migrate blog post "${post.title}": ${result.error}`);
        }
      }

      return {
        success: errors.length === 0,
        count: successCount,
        total: blogPosts.length,
        errors
      };
    } catch (error) {
      return {
        success: false,
        count: 0,
        total: blogPosts.length,
        error: error.message
      };
    }
  }

  /**
   * Migrate case studies to Firestore
   */
  static async migrateCaseStudies(createdBy) {
    try {
      let successCount = 0;
      const errors = [];

      for (const study of caseStudies) {
        const studyData = {
          title: study.title,
          clientName: study.client_name,
          clientIndustry: study.client_industry,
          challenge: study.challenge,
          solution: study.solution,
          results: study.results,
          featuredImageUrl: study.featured_image_url,
          clientLogoUrl: study.client_logo_url,
          technologies: study.technologies || [],
          duration: study.duration,
          teamSize: study.team_size
        };

        const result = await FirestoreService.addDocument(
          COLLECTIONS.CASE_STUDIES,
          studyData,
          createdBy
        );

        if (result.success) {
          successCount++;
        } else {
          errors.push(`Failed to migrate case study "${study.title}": ${result.error}`);
        }
      }

      return {
        success: errors.length === 0,
        count: successCount,
        total: caseStudies.length,
        errors
      };
    } catch (error) {
      return {
        success: false,
        count: 0,
        total: caseStudies.length,
        error: error.message
      };
    }
  }

  /**
   * Migrate testimonials to Firestore
   */
  static async migrateTestimonials(createdBy) {
    try {
      let successCount = 0;
      const errors = [];

      for (const testimonial of testimonials) {
        const testimonialData = {
          clientName: testimonial.client_name,
          clientCompany: testimonial.client_company,
          clientRole: testimonial.client_role,
          clientPhotoUrl: testimonial.client_photo_url,
          testimonialText: testimonial.testimonial_text,
          rating: testimonial.rating,
          serviceUsed: testimonial.service_used,
          videoUrl: testimonial.video_url,
          resultsAchieved: testimonial.results_achieved,
          featured: testimonial.featured || false
        };

        const result = await FirestoreService.addDocument(
          COLLECTIONS.TESTIMONIALS,
          testimonialData,
          createdBy
        );

        if (result.success) {
          successCount++;
        } else {
          errors.push(`Failed to migrate testimonial from "${testimonial.client_name}": ${result.error}`);
        }
      }

      return {
        success: errors.length === 0,
        count: successCount,
        total: testimonials.length,
        errors
      };
    } catch (error) {
      return {
        success: false,
        count: 0,
        total: testimonials.length,
        error: error.message
      };
    }
  }

  /**
   * Migrate resources to Firestore
   */
  static async migrateResources(createdBy) {
    try {
      let successCount = 0;
      const errors = [];

      for (const resource of resources) {
        const resourceData = {
          title: resource.title,
          description: resource.description,
          type: resource.type,
          fileUrl: resource.file_url,
          thumbnailUrl: resource.thumbnail_url,
          downloadCount: resource.download_count || 0,
          category: resource.category,
          tags: resource.tags || []
        };

        const result = await FirestoreService.addDocument(
          COLLECTIONS.RESOURCES,
          resourceData,
          createdBy
        );

        if (result.success) {
          successCount++;
        } else {
          errors.push(`Failed to migrate resource "${resource.title}": ${result.error}`);
        }
      }

      return {
        success: errors.length === 0,
        count: successCount,
        total: resources.length,
        errors
      };
    } catch (error) {
      return {
        success: false,
        count: 0,
        total: resources.length,
        error: error.message
      };
    }
  }

  /**
   * Migrate contact messages to Firestore
   */
  static async migrateContactMessages() {
    try {
      let successCount = 0;
      const errors = [];

      for (const message of contactMessages) {
        const messageData = {
          name: message.name,
          email: message.email,
          phone: message.phone,
          company: message.company,
          subject: message.subject,
          message: message.message,
          service: message.service || '',
          budget: message.budget || '',
          timeline: message.timeline || '',
          status: message.status || 'new',
          priority: message.priority || 'medium',
          assignedTo: message.assigned_to,
          notes: message.notes || []
        };

        const result = await FirestoreService.addDocument(
          COLLECTIONS.CONTACT_MESSAGES,
          messageData
        );

        if (result.success) {
          successCount++;
        } else {
          errors.push(`Failed to migrate contact message from "${message.name}": ${result.error}`);
        }
      }

      return {
        success: errors.length === 0,
        count: successCount,
        total: contactMessages.length,
        errors
      };
    } catch (error) {
      return {
        success: false,
        count: 0,
        total: contactMessages.length,
        error: error.message
      };
    }
  }

  /**
   * Check if data has already been migrated
   */
  static async checkMigrationStatus() {
    try {
      const collections = [
        COLLECTIONS.GALLERY_IMAGES,
        COLLECTIONS.BLOG_POSTS,
        COLLECTIONS.CASE_STUDIES,
        COLLECTIONS.TESTIMONIALS,
        COLLECTIONS.RESOURCES,
        COLLECTIONS.CONTACT_MESSAGES
      ];

      const status = {};

      for (const collection of collections) {
        const result = await FirestoreService.getDocuments(collection, { limit: 1 });
        status[collection] = {
          hasData: result.success && result.data.length > 0,
          count: result.success ? result.data.length : 0
        };
      }

      return {
        success: true,
        status
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default DataMigration;
